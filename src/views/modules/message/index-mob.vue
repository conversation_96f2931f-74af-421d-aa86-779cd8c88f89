<template>

  <div class="mobile-message-page">
    <!-- 页面标题栏 -->

    <!-- <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">待办</h1>
        <div class="header-actions">
          <n-button text @click="openSearch" class="action-btn">
            <template #icon>
              <n-icon size="20">
                <search-outline />
              </n-icon>
            </template>
          </n-button>
          <n-button text @click="showMoreActions" class="action-btn">
            <template #icon>
              <n-icon size="20">
                <ellipsis-horizontal-outline />
              </n-icon>
            </template>
          </n-button>
        </div>
      </div>
    </div> -->

    <!-- 搜索栏（可展开） -->
    <div class="search-container" v-if="showSearch">
      <div class="search-content">
        <n-input
          v-model:value="searchKeyword"
          placeholder="搜索消息..."
          clearable
          size="medium"
          class="search-input"
        >
          <template #prefix>
            <n-icon size="16">
              <search-outline />
            </n-icon>
          </template>
        </n-input>
        <n-button text @click="closeSearch" class="search-cancel">
          取消
        </n-button>
      </div>
    </div>

    <!-- 消息标签页 -->
    <div class="message-tabs-wrapper">
      <n-tabs 
        type="line" 
        animated 
        v-model:value="activeTab" 
        class="message-tabs"
        size="small"
      >

      <n-tab-pane name="later" :tab="renderTabWithBadge('later', '稍后处理')">
          <MessageList 
            :messages="getMessagesByStatus('later')" 
            :loading="loading"
            @message-click="handleMessageClick"
            @refresh="loadMessages"
          />
        </n-tab-pane>
        <n-tab-pane name="pending" :tab="renderTabWithBadge('pending', '我已处理')">
          <MessageList 
            :messages="getMessagesByStatus('pending')" 
            :loading="loading"
            @message-click="handleMessageClick"
            @refresh="loadMessages"
          />
        </n-tab-pane>

      
        
        <n-tab-pane name="created" :tab="renderTabWithBadge('created', '我创建的')">
          <MessageList 
            :messages="getMessagesByStatus('created')" 
            :loading="loading"
            @message-click="handleMessageClick"
            @refresh="loadMessages"
          />
        </n-tab-pane>
        
        <n-tab-pane name="participated" :tab="renderTabWithBadge('participated', '我参与的')">
          <MessageList 
            :messages="getMessagesByStatus('participated')" 
            :loading="loading"
            @message-click="handleMessageClick"
            @refresh="loadMessages"
          />
        </n-tab-pane>
        
    
        
        <n-tab-pane name="starred" :tab="renderTabWithBadge('starred', '星标')">
          <MessageList 
            :messages="getMessagesByStatus('starred')" 
            :loading="loading"
            @message-click="handleMessageClick"
            @refresh="loadMessages"
          />
        </n-tab-pane>
      </n-tabs>
    </div>

    <!-- 更多操作菜单 -->
    <n-dropdown
      v-model:show="showActions"
      :options="actionOptions"
      @select="handleActionSelect"
      placement="bottom-end"
      class="actions-dropdown"
    >
      <div></div>
    </n-dropdown>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted, h } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage, NBadge, NDropdown } from 'naive-ui'
import { 
  SearchOutline,
  EllipsisHorizontalOutline,
  CheckmarkOutline,
  StarOutline,
  RefreshOutline,
  SettingsOutline
} from '@vicons/ionicons5'
import { queryMessageList, updateReadFlag } from '@/api/sys/message'
import { 
  sysMessageTypeInfo, 
  getMessageTypeByGroup 
} from '@/types/sys/SysMessageTypeEnum'
import { messageHandlerService } from '@/utils/messageHandlerService'
import MessageList from './components/MessageList.vue'

// 定义消息项的接口
interface MessageItem {
  id: string | number
  title: string
  pushText: string
  pushTime: string
  gotoUrl: string
  type: string | number
  avatarUrl?: string
  thumbnailUrl?: string
  readFlag?: number // 0未读 1已读
  status?: string // 消息状态：pending, created, participated, later, starred
  priority?: 'high' | 'normal' | 'low' // 优先级
  [key: string]: any
}

export default defineComponent({
  name: 'MobileMessagePage',
  components: {
    SearchOutline,
    EllipsisHorizontalOutline,
    CheckmarkOutline,
    StarOutline,
    RefreshOutline,
    SettingsOutline,
    MessageList,
  },
  setup() {
    const router = useRouter()
    const message = useMessage()
    
    // 响应式数据
    const activeTab = ref('later')
    const messageList = ref<MessageItem[]>([])
    const loading = ref(false)
    const showSearch = ref(false)
    const searchKeyword = ref('')
    const showActions = ref(false)

    // 更多操作选项
    const actionOptions = [
      {
        label: '全部标为已读',
        key: 'markAllRead',
        icon: () => h(CheckmarkOutline),
      },
      {
        label: '刷新',
        key: 'refresh',
        icon: () => h(RefreshOutline),
      },
      {
        label: '设置',
        key: 'settings',
        icon: () => h(SettingsOutline),
      },
    ]

    // 计算属性
    const filteredMessages = computed(() => {
      if (!searchKeyword.value) return messageList.value
      
      return messageList.value.filter(item => 
        item.title.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
        item.pushText.toLowerCase().includes(searchKeyword.value.toLowerCase())
      )
    })

    // 方法
    const methods = {
      // 打开搜索
      openSearch() {
        showSearch.value = true
      },

      // 关闭搜索
      closeSearch() {
        showSearch.value = false
        searchKeyword.value = ''
      },

      // 显示更多操作
      showMoreActions() {
        showActions.value = true
      },

      // 处理操作选择
      handleActionSelect(key: string) {
        switch (key) {
          case 'markAllRead':
            methods.markAllAsRead()
            break
          case 'refresh':
            methods.loadMessages()
            break
          case 'settings':
            // 跳转到设置页面
            router.push('/message/settings')
            break
        }
        showActions.value = false
      },

      // 加载消息列表
      async loadMessages() {
        loading.value = true
        try {
          const params = {
            pageNum: 1,
            pageSize: 200,
          }
          const res = await queryMessageList(params)
          if (res.code === 200) {
            // 模拟消息状态分类
            const messages = (res.data.records || []).map((item: any) => ({
              ...item,
              avatarUrl: item.avatarUrl || '',
              status: methods.getMessageStatus(item),
              priority: methods.getMessagePriority(item),
            }))
            messageList.value = messages
          }
        } catch (error) {
          console.error('加载消息失败:', error)
          message.error('加载消息失败')
        } finally {
          loading.value = false
        }
      },

      // 获取消息状态（模拟逻辑）
      getMessageStatus(item: any): string {
        // 根据消息类型和状态判断分类
        if (item.readFlag === 1) return 'pending'
        if (item.type === '1') return 'created'
        if (item.type === '2') return 'participated'
        if (item.type === '3') return 'later'
        if (item.starred) return 'starred'
        return 'pending'
      },

      // 获取消息优先级（模拟逻辑）
      getMessagePriority(item: any): 'high' | 'normal' | 'low' {
        if (item.title.includes('紧急') || item.title.includes('重要')) return 'high'
        if (item.title.includes('普通')) return 'normal'
        return 'normal'
      },

      // 按状态获取消息
      getMessagesByStatus(status: string) {
        const filtered = filteredMessages.value.filter(item => {
          if (status === 'pending') {
            // 我已处理：已读的消息
            return item.readFlag === 1
          } else if (status === 'created') {
            // 我创建的：模拟创建者ID匹配
            return item.status === 'created'
          } else if (status === 'participated') {
            // 我参与的：模拟参与者ID匹配
            return item.status === 'participated'
          } else if (status === 'later') {
            // 稍后处理：未读消息
            return item.readFlag === 0
          } else if (status === 'starred') {
            // 星标：模拟星标状态
            return item.status === 'starred'
          }
          return false
        })
        return filtered
      },

      // 渲染带徽标的标签
      renderTabWithBadge(statusCode: string, statusName: string) {
        const count = methods.getMessagesByStatus(statusCode).length
        
        return h('div', { class: 'tab-with-badge' }, [
          h('span', { class: 'tab-name' }, statusName),
          count > 0 ? h(NBadge, { 
            value: count, 
            showZero: false,
            offset: [8, -8],
            size: 'small',
            type: statusCode === 'later' ? 'error' : 'info'
          }) : null
        ])
      },

      // 标记所有消息为已读
      async markAllAsRead() {
        try {
          const res = await updateReadFlag({})
          if (res.code === 200) {
            // 更新本地数据
            messageList.value.forEach(item => {
              item.readFlag = 1
            })
            message.success('已将所有消息标记为已读')
          }
        } catch (error) {
          message.error('操作失败，请稍后重试')
        }
      },

      // 处理消息点击
      handleMessageClick(messageData: MessageItem) {
        // 使用消息处理服务处理消息
        messageHandlerService.handleMessage(messageData)

        // 标记为已读
        if (messageData.readFlag === 0) {
          messageData.readFlag = 1
          if (messageData.id) {
            updateReadFlag({ id: Number(messageData.id) })
          }
        }
      },
    }

    // 生命周期
    onMounted(() => {
      methods.loadMessages()
    })

    return {
      activeTab,
      messageList,
      loading,
      showSearch,
      searchKeyword,
      showActions,
      actionOptions,
      filteredMessages,
      ...methods,
    }
  },
})
</script>

<style scoped>
@reference "tailwindcss";

.mobile-message-page {
  @apply min-h-screen bg-gray-50 flex flex-col;
}

/* 页面标题栏 */
.page-header {
  @apply bg-white border-b border-gray-200 sticky top-0 z-50;
}

.header-content {
  @apply flex items-center justify-between px-4 py-3;
}

.page-title {
  @apply text-lg font-semibold text-gray-900;
}

.header-actions {
  @apply flex items-center gap-2;
}

.action-btn {
  @apply p-2 rounded-full hover:bg-gray-100 transition-colors;
}

/* 搜索栏 */
.search-container {
  @apply bg-white border-b border-gray-200 px-4 py-3;
}

.search-content {
  @apply flex items-center gap-3;
}

.search-input {
  @apply flex-1;
}

.search-cancel {
  @apply text-blue-500 font-medium;
}

/* 消息标签页 */
.message-tabs-wrapper {
  @apply flex-1 bg-white;
}

.message-tabs {
  @apply h-full;
}

.tab-with-badge {
  @apply flex items-center gap-1 relative;
}

.tab-name {
  @apply text-sm;
}

/* 自定义标签页样式 */
:deep(.n-tabs-nav) {
  @apply px-4 bg-white;
}

:deep(.n-tabs-tab) {
  @apply px-3 py-3 text-sm font-medium;
}

:deep(.n-tabs-tab--active) {
  @apply text-blue-600;
}

:deep(.n-tabs-bar) {
  @apply bg-blue-600;
}

:deep(.n-tabs-pane) {
  @apply h-full;
}

/* 更多操作下拉菜单 */
.actions-dropdown {
  @apply fixed top-16 right-4 z-50;
}

/* 响应式优化 */
@media (max-width: 480px) {
  .page-title {
    @apply text-base;
  }
  
  .search-container {
    @apply px-3 py-2;
  }
  
  :deep(.n-tabs-nav) {
    @apply px-2;
  }
  
  :deep(.n-tabs-tab) {
    @apply px-2 text-xs;
  }
}
</style>
